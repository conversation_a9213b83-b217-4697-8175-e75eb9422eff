import {
  <PERSON><PERSON>rray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  MaxLength,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateQuestionDto {
  @ApiProperty({
    description: 'The text content of the question',
    type: String,
    example: 'What is the capital of France?',
  })
  @IsNotEmpty()
  @IsString()
  questionText?: string;

  // imagePath for upload questions image have to be binary file
  @ApiProperty({
    description: 'Image file for the question',
    type: 'string',
    format: 'binary',
    required: false,
  })
  @IsOptional()
  imagePath?: any; // ไม่ต้องใช้ @IsString()

  @ApiProperty({
    description: 'Whether this question serves as a header/section title',
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isHeader?: boolean;

  @ApiProperty({
    description: 'The order/position of this question in the assessment',
    type: Number,
    minimum: 1,
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  sequence?: number;

  @ApiProperty({
    description: 'Maximum file size limit in bytes for file upload questions',
    type: Number,
    required: false,
    minimum: 1,
    example: 5242880, // 5MB
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  sizeLimit?: number;

  @ApiProperty({
    description:
      'Accepted file types for file upload questions (array of MIME types)',
    type: [String],
    required: false,
    example: [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  acceptFile?: string[];

  @ApiProperty({
    description:
      'Maximum number of files that can be uploaded for this question',
    type: Number,
    required: false,
    minimum: 1,
    example: 3,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  uploadLimit?: number;

  @ApiProperty({
    description: 'ID of the item block this question belongs to',
    type: Number,
    required: false,
  })
  @IsOptional()
  itemBlockId?: number;

  @ApiProperty({
    description: 'Score for the question',
    type: Number,
    required: false,
    default: null,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  score?: number;

  @ApiProperty({
    description: 'Width of the image in pixels',
    type: Number,
    required: false,
    example: 800,
  })
  @IsOptional()
  @IsNumber()
  imageWidth?: number;

  @ApiProperty({
    description: 'Height of the image in pixels',
    type: Number,
    required: false,
    example: 600,
  })
  @IsOptional()
  @IsNumber()
  imageHeight?: number;
}
